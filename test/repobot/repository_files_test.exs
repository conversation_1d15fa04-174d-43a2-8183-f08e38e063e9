defmodule Repobot.RepositoryFilesTest do
  use Repobot.DataCase, async: true
  use Repobot.Test.Fixtures

  import Mox

  alias Repobot.RepositoryFiles
  alias Repobot.RepositoryFile
  alias Repobot.FileContent
  alias Repobot.Repo

  setup :set_mox_from_context
  setup :verify_on_exit!

  describe "list_repository_files/1" do
    test "returns all repository files for a given repository" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      file1 = create_repository_file(%{repository_id: repository.id, path: "file1.txt"})
      file2 = create_repository_file(%{repository_id: repository.id, path: "file2.txt"})

      # Create file in different repository to ensure filtering works
      other_repo = create_repository(%{user_id: user.id})
      _other_file = create_repository_file(%{repository_id: other_repo.id, path: "other.txt"})

      files = RepositoryFiles.list_repository_files(repository)

      assert length(files) == 2
      file_ids = Enum.map(files, & &1.id)
      assert file1.id in file_ids
      assert file2.id in file_ids
    end

    test "returns empty list when repository has no files" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      files = RepositoryFiles.list_repository_files(repository)

      assert files == []
    end
  end

  describe "get_repository_file!/1" do
    test "returns repository file when it exists" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})
      file = create_repository_file(%{repository_id: repository.id, path: "test.txt"})

      result = RepositoryFiles.get_repository_file!(file.id)

      assert result.id == file.id
      assert result.path == "test.txt"
    end

    test "raises when repository file does not exist" do
      assert_raise Ecto.NoResultsError, fn ->
        RepositoryFiles.get_repository_file!(Ecto.UUID.generate())
      end
    end
  end

  describe "get_repository_file_by_path/2" do
    test "returns repository file when path exists" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})
      file = create_repository_file(%{repository_id: repository.id, path: "lib/test.ex"})

      result = RepositoryFiles.get_repository_file_by_path(repository.id, "lib/test.ex")

      assert result.id == file.id
      assert result.path == "lib/test.ex"
    end

    test "returns nil when path does not exist" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      result = RepositoryFiles.get_repository_file_by_path(repository.id, "nonexistent.txt")

      assert result == nil
    end

    test "returns nil when path exists in different repository" do
      user = create_user()
      repository1 = create_repository(%{user_id: user.id})
      repository2 = create_repository(%{user_id: user.id})

      _file = create_repository_file(%{repository_id: repository1.id, path: "test.txt"})

      result = RepositoryFiles.get_repository_file_by_path(repository2.id, "test.txt")

      assert result == nil
    end
  end

  describe "create_repository_file/1" do
    test "creates repository file with valid attributes" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      attrs = %{
        repository_id: repository.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 1024,
        sha: "abc123"
      }

      assert {:ok, file} = RepositoryFiles.create_repository_file(attrs)
      assert file.repository_id == repository.id
      assert file.path == "lib/test.ex"
      assert file.name == "test.ex"
      assert file.type == "file"
      assert file.size == 1024
      assert file.sha == "abc123"
    end

    test "creates repository file with content and FileContent record" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      attrs = %{
        repository_id: repository.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 1024,
        sha: "abc123",
        content: "defmodule Test do\n  def hello, do: :world\nend"
      }

      assert {:ok, file} = RepositoryFiles.create_repository_file(attrs)
      assert file.file_content_id != nil

      # Verify FileContent was created
      file_content = Repo.get!(FileContent, file.file_content_id)
      assert file_content.repository_file_id == file.id
      # Exclusive content
      assert file_content.source_file_id == nil
      assert FileContent.exclusive?(file_content)
    end

    test "fails with invalid attributes" do
      attrs = %{
        path: "lib/test.ex",
        # Missing required repository_id
        name: "test.ex",
        type: "file"
      }

      assert {:error, changeset} = RepositoryFiles.create_repository_file(attrs)
      assert "can't be blank" in errors_on(changeset).repository_id
    end

    test "fails when repository does not exist" do
      attrs = %{
        repository_id: Ecto.UUID.generate(),
        path: "lib/test.ex",
        name: "test.ex",
        type: "file"
      }

      assert {:error, changeset} = RepositoryFiles.create_repository_file(attrs)
      assert "does not exist" in errors_on(changeset).repository_id
    end

    test "fails when path is not unique within repository" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      # Create first file
      attrs = %{
        repository_id: repository.id,
        path: "lib/test.ex",
        name: "test.ex",
        type: "file"
      }

      assert {:ok, _file} = RepositoryFiles.create_repository_file(attrs)

      # Try to create second file with same path
      assert {:error, changeset} = RepositoryFiles.create_repository_file(attrs)
      assert "has already been taken" in errors_on(changeset).repository_id
    end
  end

  describe "update_repository_file/2" do
    test "updates repository file with valid attributes" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})
      file = create_repository_file(%{repository_id: repository.id, path: "test.txt", size: 100})

      attrs = %{size: 200, sha: "new_sha"}

      assert {:ok, updated_file} = RepositoryFiles.update_repository_file(file, attrs)
      assert updated_file.size == 200
      assert updated_file.sha == "new_sha"
      # Unchanged
      assert updated_file.path == "test.txt"
    end

    test "updates repository file content and creates FileContent when content provided" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})
      file = create_repository_file(%{repository_id: repository.id, path: "test.txt"})

      new_content = "Updated file content"
      attrs = %{content: new_content}

      assert {:ok, updated_file} = RepositoryFiles.update_repository_file(file, attrs)
      assert updated_file.file_content_id != nil

      # Verify FileContent was created/updated
      file_content = Repo.get!(FileContent, updated_file.file_content_id)
      assert file_content.repository_file_id == updated_file.id
      # Exclusive content
      assert file_content.source_file_id == nil
      assert FileContent.exclusive?(file_content)
    end

    test "updates existing FileContent when file already has content" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      # Create file with initial content
      file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "test.txt",
          content: "Initial content"
        })

      initial_file_content_id = file.file_content_id

      new_content = "Updated content"
      attrs = %{content: new_content}

      assert {:ok, updated_file} = RepositoryFiles.update_repository_file(file, attrs)
      # Same FileContent record
      assert updated_file.file_content_id == initial_file_content_id

      # Verify FileContent was updated
      file_content = Repo.get!(FileContent, updated_file.file_content_id)
      # Note: Content comparison would need to decode base64 in real implementation
    end

    test "fails with invalid attributes" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})
      file = create_repository_file(%{repository_id: repository.id, path: "test.txt"})

      # Invalid - path cannot be nil
      attrs = %{path: nil}

      assert {:error, changeset} = RepositoryFiles.update_repository_file(file, attrs)
      assert "can't be blank" in errors_on(changeset).path
    end
  end

  describe "delete_repository_file/1" do
    test "deletes repository file successfully" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})
      file = create_repository_file(%{repository_id: repository.id, path: "test.txt"})

      assert {:ok, deleted_file} = RepositoryFiles.delete_repository_file(file)
      assert deleted_file.id == file.id

      # Verify file is deleted
      assert_raise Ecto.NoResultsError, fn ->
        RepositoryFiles.get_repository_file!(file.id)
      end
    end

    test "deletes associated FileContent when file has exclusive content" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "test.txt",
          content: "Some content"
        })

      file_content_id = file.file_content_id

      assert {:ok, _deleted_file} = RepositoryFiles.delete_repository_file(file)

      # Verify FileContent is also deleted (due to foreign key constraints)
      assert Repo.get(FileContent, file_content_id) == nil
    end
  end

  describe "change_repository_file/2" do
    test "returns changeset for repository file" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})
      file = create_repository_file(%{repository_id: repository.id, path: "test.txt"})

      changeset = RepositoryFiles.change_repository_file(file)

      assert %Ecto.Changeset{} = changeset
      assert changeset.data == file
    end

    test "returns changeset with changes applied" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})
      file = create_repository_file(%{repository_id: repository.id, path: "test.txt"})

      attrs = %{size: 500}
      changeset = RepositoryFiles.change_repository_file(file, attrs)

      assert %Ecto.Changeset{} = changeset
      assert changeset.changes.size == 500
    end
  end

  describe "fetch_file_content/2" do
    test "fetches content from GitHub and updates FileContent" do
      user = create_user()

      repository =
        create_repository(%{
          user_id: user.id,
          owner: "testowner",
          name: "testrepo"
        })

      file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "lib/test.ex",
          sha: "abc123"
        })

      # base64 encoded
      github_content = "ZGVmbW9kdWxlIFRlc3QgZG8KICBkZWYgaGVsbG8sIGRvOiA6d29ybGQKZW5k"

      expect(Repobot.Test.GitHubMock, :client, fn ^user -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_file_content, fn
        :mock_client, "testowner", "testrepo", "lib/test.ex" ->
          {:ok, github_content, %{}}
      end)

      assert {:ok, updated_file} = RepositoryFiles.fetch_file_content(file, user)
      assert updated_file.file_content_id != nil
      assert updated_file.content_updated_at != nil

      # Verify FileContent was created with GitHub content
      file_content = Repo.get!(FileContent, updated_file.file_content_id)
      assert file_content.repository_file_id == updated_file.id
      # Exclusive content
      assert file_content.source_file_id == nil
      assert FileContent.exclusive?(file_content)
    end

    test "updates existing FileContent when file already has content" do
      user = create_user()

      repository =
        create_repository(%{
          user_id: user.id,
          owner: "testowner",
          name: "testrepo"
        })

      # Create file with initial content
      file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "lib/test.ex",
          content: "Initial content"
        })

      initial_file_content_id = file.file_content_id
      # base64 encoded
      github_content = "VXBkYXRlZCBjb250ZW50IGZyb20gR2l0SHVi"

      expect(Repobot.Test.GitHubMock, :client, fn ^user -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_file_content, fn
        :mock_client, "testowner", "testrepo", "lib/test.ex" ->
          {:ok, github_content, %{}}
      end)

      assert {:ok, updated_file} = RepositoryFiles.fetch_file_content(file, user)
      # Same FileContent record
      assert updated_file.file_content_id == initial_file_content_id
      assert updated_file.content_updated_at != nil
    end

    test "returns error when GitHub API fails" do
      user = create_user()

      repository =
        create_repository(%{
          user_id: user.id,
          owner: "testowner",
          name: "testrepo"
        })

      file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "lib/test.ex"
        })

      expect(Repobot.Test.GitHubMock, :client, fn ^user -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_file_content, fn
        :mock_client, "testowner", "testrepo", "lib/test.ex" ->
          {:error, "File not found"}
      end)

      assert {:error, "File not found"} = RepositoryFiles.fetch_file_content(file, user)
    end
  end

  describe "get_files_at_path/2" do
    test "returns root files when path is '/'" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      # Create files at different levels
      root_file = create_repository_file(%{repository_id: repository.id, path: "README.md"})
      _nested_file = create_repository_file(%{repository_id: repository.id, path: "lib/test.ex"})

      _deep_nested =
        create_repository_file(%{repository_id: repository.id, path: "lib/deep/nested.ex"})

      files = RepositoryFiles.get_files_at_path(repository, "/")

      assert length(files) == 1
      assert hd(files).id == root_file.id
    end

    test "returns immediate children of a directory" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      # Create files in lib directory
      lib_file = create_repository_file(%{repository_id: repository.id, path: "lib/test.ex"})
      lib_file2 = create_repository_file(%{repository_id: repository.id, path: "lib/other.ex"})

      _deep_nested =
        create_repository_file(%{repository_id: repository.id, path: "lib/deep/nested.ex"})

      _root_file = create_repository_file(%{repository_id: repository.id, path: "README.md"})

      files = RepositoryFiles.get_files_at_path(repository, "lib")

      assert length(files) == 2
      file_ids = Enum.map(files, & &1.id)
      assert lib_file.id in file_ids
      assert lib_file2.id in file_ids
    end

    test "returns specific file when path points to a file" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      target_file = create_repository_file(%{repository_id: repository.id, path: "lib/test.ex"})
      _other_file = create_repository_file(%{repository_id: repository.id, path: "lib/other.ex"})

      files = RepositoryFiles.get_files_at_path(repository, "lib/test.ex")

      assert length(files) == 1
      assert hd(files).id == target_file.id
    end

    test "returns empty list when path does not exist" do
      user = create_user()
      repository = create_repository(%{user_id: user.id})

      files = RepositoryFiles.get_files_at_path(repository, "nonexistent")

      assert files == []
    end
  end

  describe "sync_repository_files/2" do
    test "syncs repository files from GitHub tree" do
      user = create_user()

      repository =
        create_repository(%{
          user_id: user.id,
          owner: "testowner",
          name: "testrepo"
        })

      github_tree = [
        %{"path" => "README.md", "type" => "file", "size" => 1024, "sha" => "abc123"},
        %{"path" => "lib/test.ex", "type" => "file", "size" => 512, "sha" => "def456"}
      ]

      expect(Repobot.Test.GitHubMock, :client, fn ^user -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_tree, fn
        :mock_client, "testowner", "testrepo" ->
          {:ok, github_tree}
      end)

      assert {:ok, files} = RepositoryFiles.sync_repository_files(repository, user)
      assert length(files) == 2

      # Verify files were created in database
      db_files = RepositoryFiles.list_repository_files(repository)
      assert length(db_files) == 2

      readme = Enum.find(db_files, &(&1.path == "README.md"))
      assert readme.size == 1024
      assert readme.sha == "abc123"

      test_file = Enum.find(db_files, &(&1.path == "lib/test.ex"))
      assert test_file.size == 512
      assert test_file.sha == "def456"
    end

    test "updates existing files when SHA changes" do
      user = create_user()

      repository =
        create_repository(%{
          user_id: user.id,
          owner: "testowner",
          name: "testrepo"
        })

      # Create existing file
      existing_file =
        create_repository_file(%{
          repository_id: repository.id,
          path: "README.md",
          size: 500,
          sha: "old_sha"
        })

      github_tree = [
        %{"path" => "README.md", "type" => "file", "size" => 1024, "sha" => "new_sha"}
      ]

      expect(Repobot.Test.GitHubMock, :client, fn ^user -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_tree, fn
        :mock_client, "testowner", "testrepo" ->
          {:ok, github_tree}
      end)

      assert {:ok, files} = RepositoryFiles.sync_repository_files(repository, user)
      assert length(files) == 1

      # Verify file was updated
      updated_file = RepositoryFiles.get_repository_file!(existing_file.id)
      assert updated_file.size == 1024
      assert updated_file.sha == "new_sha"
      # Content cleared for refetch
      assert updated_file.content_updated_at == nil
    end

    test "deletes files that no longer exist in GitHub" do
      user = create_user()

      repository =
        create_repository(%{
          user_id: user.id,
          owner: "testowner",
          name: "testrepo"
        })

      # Create existing files
      _file_to_keep =
        create_repository_file(%{
          repository_id: repository.id,
          path: "README.md"
        })

      file_to_delete =
        create_repository_file(%{
          repository_id: repository.id,
          path: "old_file.txt"
        })

      github_tree = [
        %{"path" => "README.md", "type" => "file", "size" => 1024, "sha" => "abc123"}
        # old_file.txt is not in the tree, so it should be deleted
      ]

      expect(Repobot.Test.GitHubMock, :client, fn ^user -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_tree, fn
        :mock_client, "testowner", "testrepo" ->
          {:ok, github_tree}
      end)

      assert {:ok, files} = RepositoryFiles.sync_repository_files(repository, user)
      assert length(files) == 1

      # Verify file was deleted
      assert Repo.get(RepositoryFile, file_to_delete.id) == nil

      # Verify remaining file still exists
      remaining_files = RepositoryFiles.list_repository_files(repository)
      assert length(remaining_files) == 1
      assert hd(remaining_files).path == "README.md"
    end

    test "returns error when GitHub API fails" do
      user = create_user()

      repository =
        create_repository(%{
          user_id: user.id,
          owner: "testowner",
          name: "testrepo"
        })

      expect(Repobot.Test.GitHubMock, :client, fn ^user -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_tree, fn
        :mock_client, "testowner", "testrepo" ->
          {:error, "Repository not found"}
      end)

      assert {:error, "Repository not found"} =
               RepositoryFiles.sync_repository_files(repository, user)
    end
  end
end
